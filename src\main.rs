use std::collections::HashMap;
use std::fs::{self, File};
use std::io::Write;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("============================================================");
    println!("MML数据解析器 - 华为基站外部小区数据提取工具");
    println!("Rust版本");
    println!("============================================================");
    
    // 查找输入文件
    let input_file = find_input_file()?;
    
    // 创建输出目录
    let output_dir = format!("MML解析结果_Rust_{}", 
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)?
            .as_secs()
    );
    
    fs::create_dir_all(&format!("{}/01_CSV数据文件", output_dir))?;
    fs::create_dir_all(&format!("{}/02_处理报告", output_dir))?;
    
    println!("输入文件: {}", input_file);
    println!("输出目录: {}", output_dir);
    println!();
    
    // 处理文件
    process_mml_file(&input_file, &output_dir)?;
    
    println!("\n🎉 处理完成！");
    Ok(())
}

fn find_input_file() -> Result<String, Box<dyn std::error::Error>> {
    let possible_files = [
        "基础数据/MML任务结果_WAIBU-2.txt",
        "MML任务结果_WAIBU-2.txt",
        "基础数据/MML任务结果_WAIBU.txt",
        "MML任务结果_WAIBU.txt",
    ];
    
    for file in &possible_files {
        if std::path::Path::new(file).exists() {
            println!("找到输入文件: {}", file);
            return Ok(file.to_string());
        }
    }
    
    Err("未找到输入文件".into())
}

fn process_mml_file(input_file: &str, output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 读取文件内容，处理编码问题
    let content = read_file_with_encoding(input_file)?;
    let lines: Vec<&str> = content.lines().collect();
    
    println!("总行数: {}", lines.len());
    
    let mut results: HashMap<String, Vec<Vec<String>>> = HashMap::new();
    let mut current_command: Option<String> = None;
    let mut current_station = String::new();
    let mut command_output = Vec::new();
    
    for (line_num, line) in lines.iter().enumerate() {
        if line_num % 10000 == 0 {
            println!("处理进度: {}/{} 行", line_num, lines.len());
        }
        
        let line = line.trim();
        if line.is_empty() {
            continue;
        }
        
        // 检查MML命令
        if line.contains("LST ") && line.contains("EXTERNAL") {
            // 处理之前的命令
            if let Some(cmd) = &current_command {
                process_command_output(cmd, &command_output, &current_station, input_file, &mut results)?;
            }

            // 识别新命令
            if line.contains("LST NREXTERNALCELL") && !line.contains("NREXTERNALNCELL") {
                current_command = Some("LST NREXTERNALCELL".to_string());
                println!("发现命令: LST NREXTERNALCELL (行 {})", line_num + 1);
            } else if line.contains("LST NREXTERNALNCELL") {
                current_command = Some("LST NREXTERNALNCELL".to_string());
                println!("发现命令: LST NREXTERNALNCELL (行 {})", line_num + 1);
            } else if line.contains("LST GNBEUTRAEXTERNALCELL") {
                current_command = Some("LST GNBEUTRAEXTERNALCELL".to_string());
                println!("发现命令: LST GNBEUTRAEXTERNALCELL (行 {})", line_num + 1);
            } else if line.contains("LST EUTRANEXTERNALCELL") {
                current_command = Some("LST EUTRANEXTERNALCELL".to_string());
                println!("发现命令: LST EUTRANEXTERNALCELL (行 {})", line_num + 1);
            } else {
                current_command = None;
            }

            command_output.clear();
            continue;
        }
        
        // 提取站点名称
        if line.contains("HR") && (line.contains("W") || line.contains("H")) {
            let parts: Vec<&str> = line.split_whitespace().collect();
            for part in parts {
                if part.contains("HR") && (part.contains("W") || part.contains("H")) {
                    current_station = part.to_string();
                    break;
                }
            }
        }
        
        // 收集命令输出
        if current_command.is_some() {
            command_output.push(line.to_string());
        }
    }
    
    // 处理最后一个命令
    if let Some(cmd) = &current_command {
        process_command_output(cmd, &command_output, &current_station, input_file, &mut results)?;
    }
    
    // 保存结果
    save_results(&results, output_dir)?;
    
    Ok(())
}

fn process_command_output(
    command: &str,
    output: &[String],
    station: &str,
    filename: &str,
    results: &mut HashMap<String, Vec<Vec<String>>>,
) -> Result<(), Box<dyn std::error::Error>> {
    
    // 检查执行结果 - 简化判断逻辑
    let execution_result = "执行成功";

    for line in output {
        if line.contains("非法命令") {
            return Ok(()); // 跳过非法命令
        }
    }

    // 检查是否有数据行（以460开头）
    let has_data = output.iter().any(|line| line.trim().starts_with("460"));
    if !has_data {
        return Ok(()); // 没有数据，跳过
    }
    
    // 处理数据行
    let data_lines = extract_data_lines(command, output)?;
    
    for data_line in data_lines {
        let mut record = vec![
            filename.to_string(),
            station.to_string(),
            command.to_string(),
            execution_result.to_string(),
        ];
        
        // 解析数据字段
        let fields = parse_data_fields(command, &data_line)?;
        record.extend(fields);
        
        results.entry(command.to_string()).or_insert_with(Vec::new).push(record);
    }
    
    Ok(())
}

fn extract_data_lines(command: &str, output: &[String]) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let mut data_lines = Vec::new();
    
    if command == "LST EUTRANEXTERNALCELL" || command == "LST GNBEUTRAEXTERNALCELL" {
        // 多行合并处理
        let mut current_record = Vec::new();
        let mut in_data_section = false;
        
        for line in output {
            let line = line.trim();
            
            // 跳过标题行和状态行
            if line.starts_with('-') || line.contains("执行成功") || line.contains("查询") 
                || line.contains("移动国家码") || line.contains("结果个数") || line.is_empty() {
                continue;
            }
            
            // 检查是否是新记录开始
            if line.starts_with("460") {
                if !current_record.is_empty() {
                    data_lines.push(current_record.join(" "));
                }
                current_record = vec![line.to_string()];
                in_data_section = true;
            } else if in_data_section && !line.is_empty() {
                // 过滤掉"共有x个报告"信息
                if !(line.contains("共有") && line.contains("个报告")) {
                    current_record.push(line.to_string());
                }
            }
        }
        
        if !current_record.is_empty() {
            data_lines.push(current_record.join(" "));
        }
    } else {
        // 单行处理
        for line in output {
            if line.trim().starts_with("460") {
                data_lines.push(line.trim().to_string());
            }
        }
    }
    
    Ok(data_lines)
}

fn parse_data_fields(command: &str, data_line: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let parts: Vec<&str> = data_line.split_whitespace().collect();
    let mut fields = Vec::new();
    
    match command {
        "LST NREXTERNALCELL" | "LST NREXTERNALNCELL" => {
            // 基础字段
            for i in 0..10 {
                fields.push(parts.get(i).unwrap_or(&"NULL").to_string());
            }
        }
        "LST GNBEUTRAEXTERNALCELL" => {
            // 基础字段
            for i in 0..8 {
                fields.push(parts.get(i).unwrap_or(&"NULL").to_string());
            }
            // 处理聚合属性
            if let Some(aggregate) = parts.get(8) {
                let plmn = extract_plmn_from_aggregate(aggregate);
                fields.push(plmn);
            } else {
                fields.push("NULL".to_string());
            }
            // 高速小区标识
            fields.push(parts.get(9).unwrap_or(&"NULL").to_string());
        }
        "LST EUTRANEXTERNALCELL" => {
            // 基础字段
            for i in 0..20 {
                fields.push(parts.get(i).unwrap_or(&"NULL").to_string());
            }
            // 处理聚合属性
            if let Some(aggregate) = parts.get(20) {
                let parsed = parse_aggregate_attributes(aggregate);
                fields.push(parsed.0); // 主PLMN保留标识
                fields.push(parsed.1); // 覆盖动态变更标识
                fields.push(parsed.2); // 覆盖门限自适应禁止标识
            } else {
                fields.push("NULL".to_string());
                fields.push("NULL".to_string());
                fields.push("NULL".to_string());
            }
        }
        _ => {}
    }
    
    Ok(fields)
}

fn extract_plmn_from_aggregate(aggregate: &str) -> String {
    if let Some(start) = aggregate.find("主PLMN保留标识:") {
        let after_colon = &aggregate[start + "主PLMN保留标识:".len()..];
        let value = if let Some(end) = after_colon.find('&') {
            &after_colon[..end]
        } else {
            after_colon
        };
        return value.trim().to_string();
    }
    "NULL".to_string()
}

fn parse_aggregate_attributes(aggregate: &str) -> (String, String, String) {
    let mut plmn = "NULL".to_string();
    let mut coverage_dynamic = "NULL".to_string();
    let mut coverage_threshold = "NULL".to_string();
    
    for attr in aggregate.split('&') {
        if let Some((key, value)) = attr.split_once(':') {
            let key = key.trim();
            let value = value.trim();
            
            if key.contains("主PLMN保留标识") {
                plmn = value.to_string();
            } else if key.contains("覆盖动态变更标识") {
                coverage_dynamic = value.to_string();
            } else if key.contains("覆盖门限自适应禁止标识") {
                coverage_threshold = value.to_string();
            }
        }
    }
    
    (plmn, coverage_dynamic, coverage_threshold)
}

fn save_results(results: &HashMap<String, Vec<Vec<String>>>, output_dir: &str) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n正在保存CSV文件...");
    println!("发现 {} 个命令的结果", results.len());

    for (command, records) in results {
        println!("处理命令: {} ({} 条记录)", command, records.len());
        let filename = match command.as_str() {
            "LST NREXTERNALCELL" => "查询NR外部小区.csv",
            "LST NREXTERNALNCELL" => "查询NR外部邻区.csv",
            "LST GNBEUTRAEXTERNALCELL" => "查询gNodeB_E_UTRAN外部小区.csv",
            "LST EUTRANEXTERNALCELL" => "查询EUTRAN外部小区.csv",
            _ => continue,
        };
        
        let filepath = format!("{}/01_CSV数据文件/{}", output_dir, filename);
        let mut file = File::create(&filepath)?;
        
        // 写入表头
        let headers = get_headers(command);
        writeln!(file, "{}", headers.join(","))?;
        
        // 写入数据
        for record in records {
            writeln!(file, "{}", record.join(","))?;
        }
        
        println!("保存文件: {} ({} 条记录)", filename, records.len());
    }
    
    Ok(())
}

fn get_headers(command: &str) -> Vec<String> {
    match command {
        "LST NREXTERNALCELL" => vec![
            "FileName", "NAME", "MML命令", "执行结果", "移动国家码", "移动网络码",
            "基站标识", "小区标识", "下行NR频点", "物理小区标识", "跟踪区域码",
            "小区名称", "聚合属性&主PLMN保留标识", "高速小区标识"
        ],
        "LST NREXTERNALNCELL" => vec![
            "FileName", "NAME", "MML命令", "执行结果", "移动国家码", "移动网络码",
            "基站标识", "小区标识", "下行NR频点", "物理小区标识", "跟踪区域码",
            "小区名称", "聚合属性&主PLMN保留标识", "高速小区标识"
        ],
        "LST GNBEUTRAEXTERNALCELL" => vec![
            "FileName", "NAME", "MML命令", "执行结果", "移动国家码", "移动网络码",
            "基站标识", "小区标识", "下行E-UTRAN频点", "物理小区标识", "跟踪区域码",
            "小区名称", "主PLMN保留标识", "高速小区标识"
        ],
        "LST EUTRANEXTERNALCELL" => vec![
            "FileName", "NAME", "MML命令", "执行结果", "移动国家码", "移动网络码",
            "基站标识", "小区标识", "下行频点", "上行频点配置指示", "上行频点",
            "物理小区标识", "跟踪区域码", "小区名称", "漫游区域允许切换标识",
            "ANR 标识", "专有小区标识", "高速小区指示", "上行频率偏移", "下行频率偏移",
            "NB-IoT小区指示", "外部小区更新方式&多频段指示更新方式", "支持eMTC指示",
            "控制模式", "主PLMN保留标识", "覆盖动态变更标识", "覆盖门限自适应禁止标识"
        ],
        _ => vec![]
    }.into_iter().map(|s| s.to_string()).collect()
}

fn read_file_with_encoding(file_path: &str) -> Result<String, Box<dyn std::error::Error>> {
    use std::io::Read;

    let mut file = File::open(file_path)?;
    let mut buffer = Vec::new();
    file.read_to_end(&mut buffer)?;

    // 尝试UTF-8解码
    if let Ok(content) = String::from_utf8(buffer.clone()) {
        return Ok(content);
    }

    // 如果UTF-8失败，尝试GBK解码（简化版本）
    // 这里使用lossy转换，可能会丢失一些字符，但能保证程序运行
    let content = String::from_utf8_lossy(&buffer);
    Ok(content.into_owned())
}
