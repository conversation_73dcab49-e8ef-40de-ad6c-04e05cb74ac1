# -*- coding: utf-8 -*-
# @File Name: txt数据整理.py
# @Author: 徐鑫
# @Created Time: 2025/5/30 19:40
# @SoftWare: PyCharm
import os
import shutil


class TxtData:
    def __init__(self):
        self.source_path = os.path.join(os.getcwd(), '基础数据')
        os.makedirs(self.source_path, exist_ok=True)

    def txt_data_organize(self):
        # 遍历源文件夹
        for parent, surnames, filenames in os.walk(self.source_path):
            # 遍历文件
            for filename in filenames:
                # 判断文件后缀是否为.txt
                if filename.endswith('.txt'):
                    print(filename)
                    # 读取文件内容
                    with open(os.path.join(parent, filename), 'r', encoding='gbk') as f:
                        content = f.readlines()
                        print(content)


if __name__ == '__main__':
    txt_data = TxtData()
    txt_data.txt_data_organize()
