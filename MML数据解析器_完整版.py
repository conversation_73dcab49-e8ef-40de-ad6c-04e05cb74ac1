# -*- coding: utf-8 -*-
# @File Name: MML数据解析器_完整版.py
# @Author: 徐鑫 (由AI助手优化)
# @Created Time: 2025/5/30 20:30
# @SoftWare: PyCharm
# @Description: 解析MML任务结果文件，提取外部小区和邻区信息

import os
import re
import csv
import pandas as pd
from typing import Dict, List, Tuple
import time
from datetime import datetime


class MMLDataProcessor:
    """MML数据处理器 - 解析华为基站MML命令输出数据"""

    def __init__(self):
        self.source_path = os.path.join(os.getcwd(), '基础数据')

        # 创建带时间戳的结果文件夹
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        self.main_output_path = os.path.join(os.getcwd(), f'MML解析结果_{timestamp}')

        # 创建详细的子文件夹结构
        self.csv_path = os.path.join(self.main_output_path, '01_CSV数据文件')
        self.report_path = os.path.join(self.main_output_path, '02_处理报告')
        self.summary_path = os.path.join(self.main_output_path, '03_数据汇总')
        self.log_path = os.path.join(self.main_output_path, '04_日志文件')

        # 确保所有目录存在
        os.makedirs(self.source_path, exist_ok=True)
        os.makedirs(self.csv_path, exist_ok=True)
        os.makedirs(self.report_path, exist_ok=True)
        os.makedirs(self.summary_path, exist_ok=True)
        os.makedirs(self.log_path, exist_ok=True)

        # 创建README文件说明文件夹结构
        self._create_readme_file()

    def _create_readme_file(self):
        """创建README文件说明文件夹结构"""
        readme_content = f"""# MML数据解析结果说明

## 文件夹结构说明

### 📁 01_CSV数据文件
存放解析后的CSV格式数据文件：
- 查询NR外部小区.csv - NR外部小区配置数据
- 查询NR外部邻区.csv - NR外部邻区配置数据
- 查询gNodeB_E_UTRAN外部小区.csv - gNodeB E-UTRAN外部小区数据
- 查询EUTRAN外部小区.csv - EUTRAN外部小区数据

### 📁 02_处理报告
存放详细的处理报告：
- MML数据处理报告.txt - 包含处理统计信息和按站点分类的数据统计

### 📁 03_数据汇总
存放数据汇总分析文件：
- 站点统计汇总.csv - 按站点汇总的数据统计
- 命令执行统计.csv - 各MML命令的执行情况统计

### 📁 04_日志文件
存放处理过程中的日志文件：
- 处理日志.txt - 详细的处理过程日志

## 处理时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 数据来源
基础数据文件夹中的所有txt文件

## 说明
- 所有CSV文件使用UTF-8编码，可直接用Excel打开
- 忽略了执行失败的MML命令
- 正确处理了"仍有后续报告输出"的连续数据
- 支持两种站点名称格式的识别
"""

        readme_file = os.path.join(self.main_output_path, 'README.md')
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        # 定义各种MML命令的表头
        self.headers = {
            'LST NREXTERNALCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                '基站标识', '小区标识', '下行频点', '上行频点配置指示', '上行频点',
                '物理小区标识', '跟踪区域码', '聚合属性', '主PLMN保留标识', 'NR架构选项',
                '频带', '附加频带', '小区中RedCap禁止状态'
            ],
            'LST NREXTERNALNCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                'gNodeB标识', '小区标识', '物理小区标识', '小区名称', '跟踪区域码',
                'SSB频域位置描述方式', 'SSB频域位置', 'NR架构选项', '频带', '附加频带',
                'PLMN保留标识', '高速小区标识', 'VoNR切换开关',
                '覆盖门限自适应开关', '小区保留状态开关',
                'RedCap禁止切换开关', '1RX RedCap禁止切换开关',
                '2RX RedCap禁止切换开关', '控制模式', '空中小区标识',
                'RAN通知区域标识'
            ],
            'LST GNBEUTRAEXTERNALCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                '基站标识', '小区标识', '下行E-UTRAN频点', '物理小区标识', '跟踪区域码',
                '小区名称', '聚合属性&主PLMN保留标识', '高速小区标识'
            ],
            'LST EUTRANEXTERNALCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                '基站标识', '小区标识', '下行频点', '上行频点配置指示', '上行频点',
                '物理小区标识', '跟踪区域码', '小区名称', '漫游区域允许切换标识',
                'ANR 标识', '专有小区标识', '高速小区指示', '上行频率偏移', '下行频率偏移',
                'NB-IoT小区指示', '外部小区更新方式&多频段指示更新方式', '支持eMTC指示',
                '控制模式', '主PLMN保留标识', '覆盖动态变更标识', '覆盖门限自适应禁止标识'
            ]
        }

        # 输出文件名映射
        self.output_files = {
            'LST NREXTERNALCELL': '查询NR外部小区.csv',
            'LST NREXTERNALNCELL': '查询NR外部邻区.csv',
            'LST GNBEUTRAEXTERNALCELL': '查询gNodeB_E_UTRAN外部小区.csv',
            'LST EUTRANEXTERNALCELL': '查询EUTRAN外部小区.csv'
        }

    def read_txt_files(self) -> List[Tuple[str, List[str]]]:
        """读取所有txt文件内容"""
        files_content = []

        for parent, surnames, filenames in os.walk(self.source_path):
            for filename in filenames:
                if filename.endswith('.txt'):
                    file_path = os.path.join(parent, filename)
                    try:
                        # 尝试不同的编码方式
                        for encoding in ['gbk', 'utf-8', 'gb2312', 'utf-8-sig']:
                            try:
                                with open(file_path, 'r', encoding=encoding) as f:
                                    content = f.readlines()
                                    files_content.append((filename, content))
                                    print(f"成功读取文件: {filename} (编码: {encoding})")
                                    break
                            except UnicodeDecodeError:
                                continue
                        else:
                            print(f"无法读取文件: {filename} - 编码问题")
                    except Exception as e:
                        print(f"读取文件 {filename} 时出错: {e}")

        return files_content

    def parse_mml_command_data(self, content: List[str], filename: str) -> Dict[str, List[Dict]]:
        """解析MML命令数据"""
        results = {cmd: [] for cmd in self.headers.keys()}

        current_command = None
        current_station = None
        command_output = []
        in_command_output = False

        for i, line in enumerate(content):
            line = line.strip()

            # 跳过空行
            if not line:
                continue

            # 检测站点名称
            if line.startswith('+++') and '+++' in line:
                # 提取站点名称，格式如：+++    青华为业汇B汇聚HR02W        2025-05-26 16:25:07
                parts = line.split()
                if len(parts) >= 2:
                    current_station = parts[1]
                continue

            # 检测另一种站点名称格式：网元 : 华为基地D组团HR06W
            if line.startswith('网元 :') or line.startswith('网元:'):
                # 提取站点名称
                if ':' in line:
                    station_name = line.split(':', 1)[1].strip()
                    if station_name:
                        current_station = station_name
                continue

            # 检测MML命令
            for cmd in self.headers.keys():
                if cmd in line and ('%%/*' in line or '命令-----' in line):
                    # 保存之前的命令结果
                    if current_command and command_output:
                        self._process_command_output(
                            current_command, command_output, filename,
                            current_station, results
                        )

                    current_command = cmd
                    command_output = []
                    in_command_output = True
                    break

            # 收集命令输出
            if in_command_output and current_command:
                command_output.append(line)
                # 检查是否是命令结束标志
                if '---    END' in line:
                    # 处理当前命令的输出
                    self._process_command_output(
                        current_command, command_output, filename,
                        current_station, results
                    )
                    current_command = None
                    command_output = []
                    in_command_output = False

        # 处理最后一个命令
        if current_command and command_output:
            self._process_command_output(
                current_command, command_output, filename,
                current_station, results
            )

        return results

    def _process_command_output(self, command: str, output: List[str],
                               filename: str, station: str, results: Dict):
        """处理单个命令的输出"""
        if not output:
            return

        # 判断执行结果
        execution_result = "执行失败"
        has_illegal_command = False

        for line in output:
            if '执行成功' in line:
                execution_result = "执行成功"
                break
            elif '执行失败' in line:
                execution_result = "执行失败"
                break
            elif '非法命令，不能执行' in line:
                has_illegal_command = True
                execution_result = "非法命令"
                break

        # 如果执行失败或非法命令，忽略该命令
        if execution_result == "执行失败" or has_illegal_command:
            return

        # 特殊处理EUTRANEXTERNALCELL命令（数据行可能被换行分割）
        if command == 'LST EUTRANEXTERNALCELL':
            self._process_eutranexternalcell_output(output, filename, station, execution_result, results)
            return

        # 解析数据行
        data_lines = []
        for line in output:
            # 跳过标题行、分隔符行和状态行
            if (line.startswith('-') or
                '执行成功' in line or '执行失败' in line or
                '命令执行完毕' in line or '仍有后续报告输出' in line or
                'RETCODE' in line or '%%/*' in line or
                not line.strip()):
                continue

            # 检查是否是数据行（包含数字或特定格式）
            if self._is_data_line(line):
                data_lines.append(line)

        # 如果执行成功但没有数据行，记录一条空记录表示查询成功但无数据
        if execution_result == "执行成功" and not data_lines:
            empty_record = {
                'FileName': filename,
                'NAME': station or '',
                'MML命令': command,
                '执行结果': '执行成功(无数据)'
            }
            # 填充其他字段为NULL
            for field in self.headers[command][4:]:  # 跳过前4个基础字段
                empty_record[field] = 'NULL'
            results[command].append(empty_record)
            return

        # 解析每一行数据
        for line in data_lines:
            parsed_data = self._parse_data_line(line, command, filename, station, execution_result)
            if parsed_data:
                results[command].append(parsed_data)

    def _process_eutranexternalcell_output(self, output: List[str], filename: str,
                                          station: str, execution_result: str, results: Dict):
        """专门处理LST EUTRANEXTERNALCELL命令的输出（处理多行数据格式）"""
        command = 'LST EUTRANEXTERNALCELL'

        # 合并多行数据
        merged_data_lines = []
        current_record = []
        in_data_section = False

        for line in output:
            line = line.strip()

            # 跳过标题行、分隔符行和状态行
            if (line.startswith('-') or
                '执行成功' in line or '执行失败' in line or
                '命令执行完毕' in line or '仍有后续报告输出' in line or
                'RETCODE' in line or '%%/*' in line or
                '查询EUTRAN外部小区' in line or
                '移动国家码' in line or '移动网络码' in line or
                '漫游区域允许切换标识' in line or 'ANR 标识' in line or
                '支持eMTC指示' in line or '控制模式' in line or
                '结果个数' in line or 'END' in line or
                not line):
                continue

            # 检查是否是新记录的开始（以460开头）
            if line.startswith('460'):
                # 如果有之前的记录，先保存
                if current_record:
                    merged_line = ' '.join(current_record)
                    merged_data_lines.append(merged_line)

                # 开始新记录
                current_record = [line]
                in_data_section = True
            elif in_data_section and line:
                # 继续当前记录
                current_record.append(line)

        # 保存最后一个记录
        if current_record:
            merged_line = ' '.join(current_record)
            merged_data_lines.append(merged_line)

        # 如果没有数据行，记录空记录
        if not merged_data_lines:
            empty_record = {
                'FileName': filename,
                'NAME': station or '',
                'MML命令': command,
                '执行结果': '执行成功(无数据)'
            }
            # 填充其他字段为NULL
            for field in self.headers[command][4:]:
                empty_record[field] = 'NULL'
            results[command].append(empty_record)
            return

        # 解析每一行合并后的数据
        for merged_line in merged_data_lines:
            parsed_data = self._parse_data_line(merged_line, command, filename, station, execution_result)
            if parsed_data:
                results[command].append(parsed_data)

    def _is_data_line(self, line: str) -> bool:
        """判断是否是数据行"""
        line = line.strip()
        if not line:
            return False

        # 跳过表头行和分隔符行
        if (line.startswith('-') or
            '移动国家码' in line or
            '主PLMN保留标识' in line or
            '查询' in line or
            'END' in line or
            '结果个数' in line or
            '仍有后续报告输出' in line):
            return False

        # 数据行通常以460开头（移动国家码）
        return line.startswith('460') and len(line.split()) >= 3

    def _parse_data_line(self, line: str, command: str, filename: str,
                        station: str, execution_result: str) -> Dict:
        """解析单行数据"""
        # 基础数据
        data = {
            'FileName': filename,
            'NAME': station or '',
            'MML命令': command,
            '执行结果': execution_result
        }

        # 根据不同命令解析不同字段
        try:
            if command == 'LST NREXTERNALCELL':
                return self._parse_nrexternalcell(line, data)
            elif command == 'LST NREXTERNALNCELL':
                return self._parse_nrexternalncell(line, data)
            elif command == 'LST GNBEUTRAEXTERNALCELL':
                return self._parse_gnbeutraexternalcell(line, data)
            elif command == 'LST EUTRANEXTERNALCELL':
                return self._parse_eutranexternalcell(line, data)
        except Exception as e:
            print(f"解析数据行时出错: {e}, 行内容: {line}")

        return None

    def _parse_nrexternalcell(self, line: str, base_data: Dict) -> Dict:
        """解析LST NREXTERNALCELL命令的数据行"""
        fields = self._split_data_line(line)

        field_mapping = [
            '移动国家码', '移动网络码', '基站标识', '小区标识', '下行频点',
            '上行频点配置指示', '上行频点', '物理小区标识', '跟踪区域码',
            '聚合属性', '主PLMN保留标识', 'NR架构选项', '频带', '附加频带', '小区中RedCap禁止状态'
        ]

        for i, field_name in enumerate(field_mapping):
            if i < len(fields):
                value = fields[i]
                # 只有原始数据中是NULL时才显示NULL，否则显示实际值
                base_data[field_name] = value if value else 'NULL'
            else:
                base_data[field_name] = 'NULL'

        return base_data

    def _parse_nrexternalncell(self, line: str, base_data: Dict) -> Dict:
        """解析LST NREXTERNALNCELL命令的数据行"""
        fields = self._split_data_line(line)

        # 基础字段映射（不包括移动性功能指示）
        basic_field_mapping = [
            '移动国家码', '移动网络码', 'gNodeB标识', '小区标识', '物理小区标识',
            '小区名称', '跟踪区域码', 'SSB频域位置描述方式', 'SSB频域位置',
            'NR架构选项', '频带', '附加频带', 'PLMN保留标识', '高速小区标识'
        ]

        # 填充基础字段
        for i, field_name in enumerate(basic_field_mapping):
            if i < len(fields):
                value = fields[i]
                base_data[field_name] = value if value else 'NULL'
            else:
                base_data[field_name] = 'NULL'

        # 处理移动性功能指示字段（第14个字段，索引为14）
        mobility_field_index = 14
        if mobility_field_index < len(fields):
            mobility_data = fields[mobility_field_index]
            # 解析移动性功能指示字段
            parsed_mobility = self._parse_mobility_indicators(mobility_data)
            base_data.update(parsed_mobility)
        else:
            # 如果没有移动性功能指示字段，填充默认值
            base_data.update({
                'VoNR切换开关': 'NULL',
                '覆盖门限自适应开关': 'NULL',
                '小区保留状态开关': 'NULL',
                'RedCap禁止切换开关': 'NULL',
                '1RX RedCap禁止切换开关': 'NULL',
                '2RX RedCap禁止切换开关': 'NULL'
            })

        # 处理剩余字段（控制模式、空中小区标识、RAN通知区域标识）
        remaining_fields = ['控制模式', '空中小区标识', 'RAN通知区域标识']
        start_index = mobility_field_index + 1

        for i, field_name in enumerate(remaining_fields):
            field_index = start_index + i
            if field_index < len(fields):
                value = fields[field_index]
                base_data[field_name] = value if value else 'NULL'
            else:
                base_data[field_name] = 'NULL'

        return base_data

    def _parse_mobility_indicators(self, mobility_data: str) -> Dict:
        """解析移动性功能指示字段"""
        result = {
            'VoNR切换开关': 'NULL',
            '覆盖门限自适应开关': 'NULL',
            '小区保留状态开关': 'NULL',
            'RedCap禁止切换开关': 'NULL',
            '1RX RedCap禁止切换开关': 'NULL',
            '2RX RedCap禁止切换开关': 'NULL'
        }

        if not mobility_data or mobility_data == 'NULL':
            return result

        # 解析格式如：VoNR切换开关:开&覆盖门限自适应开关:开&小区保留状态开关:关&RedCap禁止切换开关:关&1RX RedCap禁止切换开关:关&2RX RedCap禁止切换开关:关
        try:
            # 按&分割各个开关
            switches = mobility_data.split('&')

            for switch in switches:
                if ':' in switch:
                    switch_name, switch_value = switch.split(':', 1)
                    switch_name = switch_name.strip()
                    switch_value = switch_value.strip()

                    # 映射到对应的字段名
                    if switch_name == 'VoNR切换开关':
                        result['VoNR切换开关'] = switch_value
                    elif switch_name == '覆盖门限自适应开关':
                        result['覆盖门限自适应开关'] = switch_value
                    elif switch_name == '小区保留状态开关':
                        result['小区保留状态开关'] = switch_value
                    elif switch_name == 'RedCap禁止切换开关':
                        result['RedCap禁止切换开关'] = switch_value
                    elif switch_name == '1RX RedCap禁止切换开关':
                        result['1RX RedCap禁止切换开关'] = switch_value
                    elif switch_name == '2RX RedCap禁止切换开关':
                        result['2RX RedCap禁止切换开关'] = switch_value

        except Exception as e:
            print(f"解析移动性功能指示字段时出错: {e}, 数据: {mobility_data}")

        return result

    def _parse_gnbeutraexternalcell(self, line: str, base_data: Dict) -> Dict:
        """解析LST GNBEUTRAEXTERNALCELL命令的数据行"""
        fields = self._split_data_line(line)

        field_mapping = [
            '移动国家码', '移动网络码', '基站标识', '小区标识', '下行E-UTRAN频点',
            '物理小区标识', '跟踪区域码', '小区名称', '聚合属性&主PLMN保留标识',
            '高速小区标识'
        ]

        for i, field_name in enumerate(field_mapping):
            if i < len(fields):
                value = fields[i]
                base_data[field_name] = value if value else 'NULL'
            else:
                base_data[field_name] = 'NULL'

        return base_data

    def _parse_eutranexternalcell(self, line: str, base_data: Dict) -> Dict:
        """解析LST EUTRANEXTERNALCELL命令的数据行"""
        fields = self._split_data_line(line)

        # 基础字段映射（不包括聚合属性）
        basic_field_mapping = [
            '移动国家码', '移动网络码', '基站标识', '小区标识', '下行频点',
            '上行频点配置指示', '上行频点', '物理小区标识', '跟踪区域码',
            '小区名称', '漫游区域允许切换标识', 'ANR 标识', '专有小区标识',
            '高速小区指示', '上行频率偏移', '下行频率偏移', 'NB-IoT小区指示',
            '外部小区更新方式&多频段指示更新方式', '支持eMTC指示', '控制模式'
        ]

        # 填充基础字段
        for i, field_name in enumerate(basic_field_mapping):
            if i < len(fields):
                value = fields[i]
                base_data[field_name] = value if value else 'NULL'
            else:
                base_data[field_name] = 'NULL'

        # 处理聚合属性字段（第20个字段，索引为20）
        aggregate_field_index = 20
        if aggregate_field_index < len(fields):
            aggregate_data = fields[aggregate_field_index]
            # 解析聚合属性字段
            parsed_aggregate = self._parse_aggregate_attributes(aggregate_data)
            base_data.update(parsed_aggregate)
        else:
            # 如果没有聚合属性字段，填充默认值
            base_data.update({
                '主PLMN保留标识': 'NULL',
                '覆盖动态变更标识': 'NULL',
                '覆盖门限自适应禁止标识': 'NULL'
            })

        return base_data

    def _parse_aggregate_attributes(self, aggregate_data: str) -> Dict:
        """解析聚合属性字段"""
        result = {
            '主PLMN保留标识': 'NULL',
            '覆盖动态变更标识': 'NULL',
            '覆盖门限自适应禁止标识': 'NULL'
        }

        if not aggregate_data or aggregate_data == 'NULL':
            return result

        # 解析格式如：主PLMN保留标识:关&覆盖动态变更标识:关&覆盖门限自适应禁止标识:关
        try:
            # 按&分割各个属性
            attributes = aggregate_data.split('&')

            for attribute in attributes:
                if ':' in attribute:
                    attr_name, attr_value = attribute.split(':', 1)
                    attr_name = attr_name.strip()
                    attr_value = attr_value.strip()

                    # 映射到对应的字段名
                    if '主PLMN保留标识' in attr_name:
                        result['主PLMN保留标识'] = attr_value
                    elif '覆盖动态变更标识' in attr_name:
                        result['覆盖动态变更标识'] = attr_value
                    elif '覆盖门限自适应禁止标识' in attr_name:
                        result['覆盖门限自适应禁止标识'] = attr_value

        except Exception as e:
            print(f"解析聚合属性字段时出错: {e}, 数据: {aggregate_data}")

        return result

    def _split_data_line(self, line: str) -> List[str]:
        """分割数据行"""
        # 清理行首尾空格
        line = line.strip()

        # 使用正则表达式分割，处理多个空格
        fields = []

        # 分割字段，考虑到某些字段可能包含空格
        parts = re.split(r'\s{2,}', line)  # 两个或更多空格分割

        for part in parts:
            part = part.strip()
            # 保持原始值，包括NULL
            fields.append(part)

        return fields

    def save_to_csv(self, results: Dict[str, List[Dict]]):
        """保存结果到CSV文件"""
        for command, data_list in results.items():
            if not data_list:
                print(f"命令 {command} 没有有效数据")
                continue

            base_filename = self.output_files[command]
            output_file = os.path.join(self.csv_path, base_filename)

            # 如果文件被占用，尝试添加时间戳
            retry_count = 0
            while retry_count < 3:
                try:
                    # 使用pandas保存CSV，确保中文编码正确
                    df = pd.DataFrame(data_list)

                    # 确保列的顺序与表头一致
                    ordered_columns = self.headers[command]

                    # 重新排序列，缺失的列用空字符串填充
                    for col in ordered_columns:
                        if col not in df.columns:
                            df[col] = ''

                    df = df[ordered_columns]

                    # 保存为CSV
                    df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"成功保存 {len(data_list)} 条记录到 {output_file}")
                    break

                except PermissionError:
                    retry_count += 1
                    if retry_count < 3:
                        # 添加时间戳重试
                        retry_timestamp = time.strftime("%Y%m%d_%H%M%S")
                        name, ext = os.path.splitext(base_filename)
                        new_filename = f"{name}_{retry_timestamp}{ext}"
                        output_file = os.path.join(self.csv_path, new_filename)
                        print(f"文件被占用，尝试保存为: {new_filename}")
                    else:
                        print(f"无法保存文件 {base_filename}，请关闭可能打开该文件的程序")

                except Exception as e:
                    print(f"保存文件 {output_file} 时出错: {e}")
                    break

    def generate_summary_report(self, results: Dict[str, List[Dict]]):
        """生成汇总报告"""
        report_file = os.path.join(self.report_path, f"MML数据处理报告_{time.strftime('%Y%m%d_%H%M%S')}.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("MML数据处理报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("处理结果统计:\n")
            f.write("-" * 30 + "\n")

            total_records = 0
            for command, data_list in results.items():
                count = len(data_list)
                total_records += count
                f.write(f"{command}: {count} 条记录\n")

            f.write(f"\n总计: {total_records} 条记录\n\n")

            # 按站点统计
            station_stats = {}
            for command, data_list in results.items():
                for record in data_list:
                    station = record.get('NAME', '未知站点')
                    if station not in station_stats:
                        station_stats[station] = {}
                    if command not in station_stats[station]:
                        station_stats[station][command] = 0
                    station_stats[station][command] += 1

            if station_stats:
                f.write("按站点统计:\n")
                f.write("-" * 30 + "\n")
                for station, commands in station_stats.items():
                    f.write(f"\n站点: {station}\n")
                    for cmd, count in commands.items():
                        f.write(f"  {cmd}: {count} 条\n")

        print(f"处理报告已保存到: {report_file}")

    def generate_summary_files(self, results: Dict[str, List[Dict]]):
        """生成数据汇总文件"""
        # 1. 生成站点统计汇总
        self._generate_station_summary(results)

        # 2. 生成命令执行统计
        self._generate_command_summary(results)

        # 3. 生成处理日志
        self._generate_process_log(results)

    def _generate_station_summary(self, results: Dict[str, List[Dict]]):
        """生成站点统计汇总CSV"""
        station_stats = {}

        # 统计每个站点的数据
        for command, data_list in results.items():
            for record in data_list:
                station = record.get('NAME', '未知站点')
                if station not in station_stats:
                    station_stats[station] = {
                        '站点名称': station,
                        'LST NREXTERNALCELL': 0,
                        'LST NREXTERNALNCELL': 0,
                        'LST GNBEUTRAEXTERNALCELL': 0,
                        'LST EUTRANEXTERNALCELL': 0,
                        '总记录数': 0
                    }
                station_stats[station][command] += 1
                station_stats[station]['总记录数'] += 1

        # 转换为DataFrame并保存
        if station_stats:
            df = pd.DataFrame(list(station_stats.values()))
            summary_file = os.path.join(self.summary_path, '站点统计汇总.csv')
            df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            print(f"站点统计汇总已保存到: {summary_file}")

    def _generate_command_summary(self, results: Dict[str, List[Dict]]):
        """生成命令执行统计CSV"""
        command_stats = []

        for command, data_list in results.items():
            # 统计执行结果
            success_count = 0
            no_data_count = 0

            for record in data_list:
                exec_result = record.get('执行结果', '')
                if '执行成功(无数据)' in exec_result:
                    no_data_count += 1
                elif '执行成功' in exec_result:
                    success_count += 1

            command_stats.append({
                'MML命令': command,
                '总记录数': len(data_list),
                '有数据记录数': success_count,
                '无数据记录数': no_data_count,
                '数据覆盖率': f"{(success_count / len(data_list) * 100):.1f}%" if data_list else "0%"
            })

        # 保存命令统计
        if command_stats:
            df = pd.DataFrame(command_stats)
            summary_file = os.path.join(self.summary_path, '命令执行统计.csv')
            df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            print(f"命令执行统计已保存到: {summary_file}")

    def _generate_process_log(self, results: Dict[str, List[Dict]]):
        """生成处理日志"""
        log_file = os.path.join(self.log_path, f"处理日志_{time.strftime('%Y%m%d_%H%M%S')}.txt")

        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("MML数据处理日志\n")
            f.write("=" * 50 + "\n")
            f.write(f"处理开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"源数据路径: {self.source_path}\n")
            f.write(f"输出路径: {self.main_output_path}\n\n")

            f.write("处理的MML命令:\n")
            f.write("-" * 30 + "\n")
            for command in self.headers.keys():
                f.write(f"- {command}\n")

            f.write(f"\n处理结果:\n")
            f.write("-" * 30 + "\n")
            total_records = 0
            for command, data_list in results.items():
                count = len(data_list)
                total_records += count
                f.write(f"{command}: {count} 条记录\n")

            f.write(f"\n总计处理记录数: {total_records}\n")
            f.write(f"处理完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

        print(f"处理日志已保存到: {log_file}")

    def process_all_files(self):
        """处理所有txt文件的主方法"""
        print("=" * 60)
        print("MML数据解析器 - 华为基站外部小区数据提取工具")
        print("=" * 60)
        print("开始处理MML数据...")

        # 读取所有txt文件
        files_content = self.read_txt_files()

        if not files_content:
            print("没有找到任何txt文件")
            return

        # 汇总所有结果
        all_results = {cmd: [] for cmd in self.headers.keys()}

        # 处理每个文件
        for filename, content in files_content:
            print(f"正在处理文件: {filename}")

            try:
                file_results = self.parse_mml_command_data(content, filename)

                # 合并结果
                for command, data_list in file_results.items():
                    all_results[command].extend(data_list)

            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")

        # 输出统计信息
        print("\n" + "=" * 40)
        print("处理结果统计:")
        print("=" * 40)
        for command, data_list in all_results.items():
            print(f"{command}: {len(data_list)} 条记录")

        # 保存到CSV文件
        print("\n正在保存CSV文件...")
        self.save_to_csv(all_results)

        # 生成汇总报告
        print("\n正在生成汇总报告...")
        self.generate_summary_report(all_results)

        # 生成数据汇总文件
        print("\n正在生成数据汇总文件...")
        self.generate_summary_files(all_results)

        print("\n" + "=" * 60)
        print("🎉 处理完成！")
        print("=" * 60)
        print(f"📁 结果文件夹: {self.main_output_path}")
        print("📋 文件夹内容:")
        print("   📁 01_CSV数据文件 - 解析后的CSV数据")
        print("   📁 02_处理报告 - 详细处理报告")
        print("   📁 03_数据汇总 - 统计汇总文件")
        print("   📁 04_日志文件 - 处理日志")
        print("   📄 README.md - 说明文档")
        print("=" * 60)


if __name__ == '__main__':
    processor = MMLDataProcessor()
    processor.process_all_files()
