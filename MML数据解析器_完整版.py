# -*- coding: utf-8 -*-
# @File Name: MML数据解析器_完整版.py
# @Author: 徐鑫 (由AI助手优化)
# @Created Time: 2025/5/30 20:30
# @SoftWare: PyCharm
# @Description: 解析MML任务结果文件，提取外部小区和邻区信息

import os
import re
import csv
import pandas as pd
from typing import Dict, List, Tuple
import time
from datetime import datetime


class MMLDataProcessor:
    """MML数据处理器 - 解析华为基站MML命令输出数据"""

    def __init__(self):
        self.source_path = os.path.join(os.getcwd(), '基础数据')
        self.output_path = os.path.join(os.getcwd(), '外部解析')
        os.makedirs(self.source_path, exist_ok=True)
        os.makedirs(self.output_path, exist_ok=True)

        # 定义各种MML命令的表头
        self.headers = {
            'LST NREXTERNALCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                '基站标识', '小区标识', '下行频点', '上行频点配置指示', '上行频点',
                '物理小区标识', '跟踪区域码', '聚合属性&控制模式标识',
                '聚合属性&漫游区域允许切换标识', '主PLMN保留标识', 'NR架构选项',
                '频带', '附加频带', '小区中RedCap禁止状态'
            ],
            'LST NREXTERNALNCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                'gNodeB标识', '小区标识', '物理小区标识', '小区名称', '跟踪区域码',
                'SSB频域位置描述方式', 'SSB频域位置', 'NR架构选项', '频带', '附加频带',
                'PLMN保留标识', '高速小区标识', '移动性功能指示&VoNR切换开关',
                '移动性功能指示&覆盖门限自适应开关', '移动性功能指示&小区保留状态开关',
                '移动性功能指示&RedCap禁止切换开关', '移动性功能指示&1RX RedCap禁止切换开关',
                '移动性功能指示&2RX RedCap禁止切换开关', '控制模式', '空中小区标识',
                'RAN通知区域标识'
            ],
            'LST GNBEUTRAEXTERNALCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                '基站标识', '小区标识', '下行E-UTRAN频点', '物理小区标识', '跟踪区域码',
                '小区名称', '聚合属性&主PLMN保留标识', '高速小区标识'
            ],
            'LST EUTRANEXTERNALCELL': [
                'FileName', 'NAME', 'MML命令', '执行结果', '移动国家码', '移动网络码',
                '基站标识', '小区标识', '下行频点', '上行频点配置指示', '上行频点',
                '物理小区标识', '跟踪区域码', '小区名称', '漫游区域允许切换标识',
                'ANR 标识', '专有小区标识', '高速小区指示', '上行频率偏移', '下行频率偏移',
                'NB-IoT小区指示', '外部小区更新方式&多频段指示更新方式', '支持eMTC指示',
                '控制模式', '聚合属性&主PLMN保留标识', '聚合属性&覆盖动态变更标识',
                '聚合属性&覆盖门限自适应禁止标识'
            ]
        }

        # 输出文件名映射
        self.output_files = {
            'LST NREXTERNALCELL': '查询NR外部小区.csv',
            'LST NREXTERNALNCELL': '查询NR外部邻区.csv',
            'LST GNBEUTRAEXTERNALCELL': '查询gNodeB_E_UTRAN外部小区.csv',
            'LST EUTRANEXTERNALCELL': '查询EUTRAN外部小区.csv'
        }

    def read_txt_files(self) -> List[Tuple[str, List[str]]]:
        """读取所有txt文件内容"""
        files_content = []

        for parent, surnames, filenames in os.walk(self.source_path):
            for filename in filenames:
                if filename.endswith('.txt'):
                    file_path = os.path.join(parent, filename)
                    try:
                        # 尝试不同的编码方式
                        for encoding in ['gbk', 'utf-8', 'gb2312', 'utf-8-sig']:
                            try:
                                with open(file_path, 'r', encoding=encoding) as f:
                                    content = f.readlines()
                                    files_content.append((filename, content))
                                    print(f"成功读取文件: {filename} (编码: {encoding})")
                                    break
                            except UnicodeDecodeError:
                                continue
                        else:
                            print(f"无法读取文件: {filename} - 编码问题")
                    except Exception as e:
                        print(f"读取文件 {filename} 时出错: {e}")

        return files_content

    def parse_mml_command_data(self, content: List[str], filename: str) -> Dict[str, List[Dict]]:
        """解析MML命令数据"""
        results = {cmd: [] for cmd in self.headers.keys()}

        current_command = None
        current_station = None
        command_output = []
        in_command_output = False

        for i, line in enumerate(content):
            line = line.strip()

            # 跳过空行
            if not line:
                continue

            # 检测站点名称
            if line.startswith('+++') and '+++' in line:
                # 提取站点名称，格式如：+++    青华为业汇B汇聚HR02W        2025-05-26 16:25:07
                parts = line.split()
                if len(parts) >= 2:
                    current_station = parts[1]
                continue

            # 检测MML命令
            for cmd in self.headers.keys():
                if cmd in line and ('%%/*' in line or '命令-----' in line):
                    # 保存之前的命令结果
                    if current_command and command_output:
                        self._process_command_output(
                            current_command, command_output, filename,
                            current_station, results
                        )

                    current_command = cmd
                    command_output = []
                    in_command_output = True
                    break

            # 收集命令输出
            if in_command_output and current_command:
                command_output.append(line)
                # 检查是否是命令结束标志
                if '---    END' in line:
                    # 处理当前命令的输出
                    self._process_command_output(
                        current_command, command_output, filename,
                        current_station, results
                    )
                    current_command = None
                    command_output = []
                    in_command_output = False

        # 处理最后一个命令
        if current_command and command_output:
            self._process_command_output(
                current_command, command_output, filename,
                current_station, results
            )

        return results

    def _process_command_output(self, command: str, output: List[str],
                               filename: str, station: str, results: Dict):
        """处理单个命令的输出"""
        if not output:
            return

        # 判断执行结果
        execution_result = "执行失败"
        for line in output:
            if '执行成功' in line:
                execution_result = "执行成功"
                break
            elif '执行失败' in line:
                execution_result = "执行失败"
                break

        # 如果执行失败，忽略该命令
        if execution_result == "执行失败":
            return

        # 解析数据行
        data_lines = []
        for line in output:
            # 跳过标题行、分隔符行和状态行
            if (line.startswith('-') or
                '执行成功' in line or '执行失败' in line or
                '命令执行完毕' in line or '仍有后续报告输出' in line or
                not line.strip()):
                continue

            # 检查是否是数据行（包含数字或特定格式）
            if self._is_data_line(line):
                data_lines.append(line)

        # 解析每一行数据
        for line in data_lines:
            parsed_data = self._parse_data_line(line, command, filename, station, execution_result)
            if parsed_data:
                results[command].append(parsed_data)

    def _is_data_line(self, line: str) -> bool:
        """判断是否是数据行"""
        line = line.strip()
        if not line:
            return False

        # 跳过表头行和分隔符行
        if (line.startswith('-') or
            '移动国家码' in line or
            '主PLMN保留标识' in line or
            '查询' in line or
            'END' in line or
            '结果个数' in line or
            '仍有后续报告输出' in line):
            return False

        # 数据行通常以460开头（移动国家码）
        return line.startswith('460') and len(line.split()) >= 3

    def _parse_data_line(self, line: str, command: str, filename: str,
                        station: str, execution_result: str) -> Dict:
        """解析单行数据"""
        # 基础数据
        data = {
            'FileName': filename,
            'NAME': station or '',
            'MML命令': command,
            '执行结果': execution_result
        }

        # 根据不同命令解析不同字段
        try:
            if command == 'LST NREXTERNALCELL':
                return self._parse_nrexternalcell(line, data)
            elif command == 'LST NREXTERNALNCELL':
                return self._parse_nrexternalncell(line, data)
            elif command == 'LST GNBEUTRAEXTERNALCELL':
                return self._parse_gnbeutraexternalcell(line, data)
            elif command == 'LST EUTRANEXTERNALCELL':
                return self._parse_eutranexternalcell(line, data)
        except Exception as e:
            print(f"解析数据行时出错: {e}, 行内容: {line}")

        return None

    def _parse_nrexternalcell(self, line: str, base_data: Dict) -> Dict:
        """解析LST NREXTERNALCELL命令的数据行"""
        fields = self._split_data_line(line)

        field_mapping = [
            '移动国家码', '移动网络码', '基站标识', '小区标识', '下行频点',
            '上行频点配置指示', '上行频点', '物理小区标识', '跟踪区域码',
            '聚合属性&控制模式标识', '聚合属性&漫游区域允许切换标识',
            '主PLMN保留标识', 'NR架构选项', '频带', '附加频带', '小区中RedCap禁止状态'
        ]

        for i, field_name in enumerate(field_mapping):
            if i < len(fields):
                base_data[field_name] = fields[i]
            else:
                base_data[field_name] = ''

        return base_data

    def _parse_nrexternalncell(self, line: str, base_data: Dict) -> Dict:
        """解析LST NREXTERNALNCELL命令的数据行"""
        fields = self._split_data_line(line)

        field_mapping = [
            '移动国家码', '移动网络码', 'gNodeB标识', '小区标识', '物理小区标识',
            '小区名称', '跟踪区域码', 'SSB频域位置描述方式', 'SSB频域位置',
            'NR架构选项', '频带', '附加频带', 'PLMN保留标识', '高速小区标识',
            '移动性功能指示&VoNR切换开关', '移动性功能指示&覆盖门限自适应开关',
            '移动性功能指示&小区保留状态开关', '移动性功能指示&RedCap禁止切换开关',
            '移动性功能指示&1RX RedCap禁止切换开关', '移动性功能指示&2RX RedCap禁止切换开关',
            '控制模式', '空中小区标识', 'RAN通知区域标识'
        ]

        for i, field_name in enumerate(field_mapping):
            if i < len(fields):
                base_data[field_name] = fields[i]
            else:
                base_data[field_name] = ''

        return base_data

    def _parse_gnbeutraexternalcell(self, line: str, base_data: Dict) -> Dict:
        """解析LST GNBEUTRAEXTERNALCELL命令的数据行"""
        fields = self._split_data_line(line)

        field_mapping = [
            '移动国家码', '移动网络码', '基站标识', '小区标识', '下行E-UTRAN频点',
            '物理小区标识', '跟踪区域码', '小区名称', '聚合属性&主PLMN保留标识',
            '高速小区标识'
        ]

        for i, field_name in enumerate(field_mapping):
            if i < len(fields):
                base_data[field_name] = fields[i]
            else:
                base_data[field_name] = ''

        return base_data

    def _parse_eutranexternalcell(self, line: str, base_data: Dict) -> Dict:
        """解析LST EUTRANEXTERNALCELL命令的数据行"""
        fields = self._split_data_line(line)

        field_mapping = [
            '移动国家码', '移动网络码', '基站标识', '小区标识', '下行频点',
            '上行频点配置指示', '上行频点', '物理小区标识', '跟踪区域码',
            '小区名称', '漫游区域允许切换标识', 'ANR 标识', '专有小区标识',
            '高速小区指示', '上行频率偏移', '下行频率偏移', 'NB-IoT小区指示',
            '外部小区更新方式&多频段指示更新方式', '支持eMTC指示', '控制模式',
            '聚合属性&主PLMN保留标识', '聚合属性&覆盖动态变更标识',
            '聚合属性&覆盖门限自适应禁止标识'
        ]

        for i, field_name in enumerate(field_mapping):
            if i < len(fields):
                base_data[field_name] = fields[i]
            else:
                base_data[field_name] = ''

        return base_data

    def _split_data_line(self, line: str) -> List[str]:
        """分割数据行"""
        # 清理行首尾空格
        line = line.strip()

        # 使用正则表达式分割，处理多个空格
        fields = []

        # 分割字段，考虑到某些字段可能包含空格
        parts = re.split(r'\s{2,}', line)  # 两个或更多空格分割

        for part in parts:
            part = part.strip()
            if part and part != 'NULL':
                fields.append(part)
            elif part == 'NULL':
                fields.append('')
            else:
                fields.append('')

        return fields

    def save_to_csv(self, results: Dict[str, List[Dict]]):
        """保存结果到CSV文件"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")

        for command, data_list in results.items():
            if not data_list:
                print(f"命令 {command} 没有有效数据")
                continue

            base_filename = self.output_files[command]
            output_file = os.path.join(self.output_path, base_filename)

            # 如果文件被占用，尝试添加时间戳
            retry_count = 0
            while retry_count < 3:
                try:
                    # 使用pandas保存CSV，确保中文编码正确
                    df = pd.DataFrame(data_list)

                    # 确保列的顺序与表头一致
                    ordered_columns = self.headers[command]

                    # 重新排序列，缺失的列用空字符串填充
                    for col in ordered_columns:
                        if col not in df.columns:
                            df[col] = ''

                    df = df[ordered_columns]

                    # 保存为CSV
                    df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"成功保存 {len(data_list)} 条记录到 {output_file}")
                    break

                except PermissionError:
                    retry_count += 1
                    if retry_count < 3:
                        # 添加时间戳重试
                        name, ext = os.path.splitext(base_filename)
                        new_filename = f"{name}_{timestamp}{ext}"
                        output_file = os.path.join(self.output_path, new_filename)
                        print(f"文件被占用，尝试保存为: {new_filename}")
                    else:
                        print(f"无法保存文件 {base_filename}，请关闭可能打开该文件的程序")

                except Exception as e:
                    print(f"保存文件 {output_file} 时出错: {e}")
                    break

    def generate_summary_report(self, results: Dict[str, List[Dict]]):
        """生成汇总报告"""
        report_file = os.path.join(self.output_path, f"处理报告_{time.strftime('%Y%m%d_%H%M%S')}.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("MML数据处理报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("处理结果统计:\n")
            f.write("-" * 30 + "\n")

            total_records = 0
            for command, data_list in results.items():
                count = len(data_list)
                total_records += count
                f.write(f"{command}: {count} 条记录\n")

            f.write(f"\n总计: {total_records} 条记录\n\n")

            # 按站点统计
            station_stats = {}
            for command, data_list in results.items():
                for record in data_list:
                    station = record.get('NAME', '未知站点')
                    if station not in station_stats:
                        station_stats[station] = {}
                    if command not in station_stats[station]:
                        station_stats[station][command] = 0
                    station_stats[station][command] += 1

            if station_stats:
                f.write("按站点统计:\n")
                f.write("-" * 30 + "\n")
                for station, commands in station_stats.items():
                    f.write(f"\n站点: {station}\n")
                    for cmd, count in commands.items():
                        f.write(f"  {cmd}: {count} 条\n")

        print(f"处理报告已保存到: {report_file}")

    def process_all_files(self):
        """处理所有txt文件的主方法"""
        print("=" * 60)
        print("MML数据解析器 - 华为基站外部小区数据提取工具")
        print("=" * 60)
        print("开始处理MML数据...")

        # 读取所有txt文件
        files_content = self.read_txt_files()

        if not files_content:
            print("没有找到任何txt文件")
            return

        # 汇总所有结果
        all_results = {cmd: [] for cmd in self.headers.keys()}

        # 处理每个文件
        for filename, content in files_content:
            print(f"正在处理文件: {filename}")

            try:
                file_results = self.parse_mml_command_data(content, filename)

                # 合并结果
                for command, data_list in file_results.items():
                    all_results[command].extend(data_list)

            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")

        # 输出统计信息
        print("\n" + "=" * 40)
        print("处理结果统计:")
        print("=" * 40)
        for command, data_list in all_results.items():
            print(f"{command}: {len(data_list)} 条记录")

        # 保存到CSV文件
        print("\n正在保存CSV文件...")
        self.save_to_csv(all_results)

        # 生成汇总报告
        print("\n正在生成汇总报告...")
        self.generate_summary_report(all_results)

        print("\n" + "=" * 40)
        print("处理完成！")
        print("=" * 40)


if __name__ == '__main__':
    processor = MMLDataProcessor()
    processor.process_all_files()
